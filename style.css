.connected-section {
  width: 100vw;
  background: #fff;
  position: relative;
  overflow-x: hidden;
  margin: 0;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connected-bg {
  width: 100%;
  max-width: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 0;
}

.connected-inner {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.connected-center {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  position: relative;
  gap: 0; /* No gap - they should overlap */
}

/* Mobile container with white background */
.mobile-container {
  background: #fff;
  border-radius: 24px;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.15);
  position: relative;
  z-index: 3;
  margin-right: -20px; /* Overlap with green section */
}

.connected-mobile {
  width: 200px;
  max-width: 25vw;
  transform: rotate(-8deg);
  transition: transform 0.3s ease;
}

.connected-mobile:hover {
  transform: rotate(-5deg) scale(1.02);
}

/* Green background only behind text content */
.connected-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  background: #9cab7a;
  padding: 50px 60px 50px 80px; /* More left padding for overlap */
  border-radius: 24px;
  position: relative;
  z-index: 2;
  min-width: 320px;
}

.connected-title {
  font-family: 'Pacifico', cursive;
  color: #fff6d6;
  font-size: 42px;
  font-weight: 400;
  margin-bottom: 20px;
  text-align: left;
  line-height: 1.1;
  letter-spacing: 0.01em;
}

.connected-stores {
  display: flex;
  gap: 32px;
  margin-top: 12px;
}

.connected-stores div {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 16px;
  font-family: 'Inter', Arial, sans-serif;
  opacity: 0.95;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.connected-stores div:hover {
  opacity: 1;
}

.connected-stores i {
  font-size: 20px;
  color: #fff;
}

.connected-dog {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 300px;
  width: auto;
  max-width: 280px;
  z-index: 4;
  pointer-events: none;
  object-fit: cover;
}

/* Responsive */
@media (max-width: 1100px) {
  .connected-content {
    padding: 40px 50px 40px 60px;
    min-width: 280px;
  }
  
  .mobile-container {
    padding: 30px;
    margin-right: -15px;
  }
  
  .connected-mobile {
    width: 160px;
  }
  
  .connected-dog {
    height: 250px;
    max-width: 200px;
  }
  
  .connected-title {
    font-size: 28px;
  }
}

@media (max-width: 700px) {
  .connected-section {
    padding: 40px 20px;
  }
  
  .connected-center {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .mobile-container {
    padding: 25px;
    margin-right: 0;
    margin-bottom: 20px;
    order: 1;
  }
  
  .connected-mobile {
    width: 140px;
    transform: rotate(-5deg);
  }
  
  .connected-content {
    align-items: center;
    text-align: center;
    padding: 30px;
    border-radius: 24px;
    min-width: auto;
    width: 100%;
    max-width: 300px;
    order: 2;
  }
  
  .connected-title {
    font-size: 24px;
    margin-bottom: 15px;
  }
  
  .connected-stores {
    gap: 25px;
    justify-content: center;
  }
  
  .connected-dog {
    position: static;
    width: 120px;
    height: auto;
    margin: 20px auto 0;
    transform: none;
    order: 3;
  }
}