    <!DOCTYPE html>
    <html lang="en">
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Example</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <style>
        .navbar {
        background:#fff; /* beige gradient */
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
        height: 50px;
        }
        .nav-link {
        color: #222 !important;
        font-weight: 500;
        margin: 0 10px; /* spacing between links */
        }
        .nav-link:hover {
        color: #000 !important;
        }
        .right-links a {
        margin-left: 20px; /* spacing for right section */
        font-weight: 500;
        }
        .hero-section {
        background: #f6edca; /* light beige */
        position: relative;
        padding: 80px 0;
        
        }
        .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #000; /* greenish */
        }
        .hero-subtext {
        font-size: 1.4rem;
        margin: 15px 0 30px;
        color: #8f9e73;
        }
        .btn-custom {
        background-color: #5d805f;
        color: #fff;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 6px;
        }
        .btn-custom:hover {
        background-color: #466447;
        color: #fff;
        }
    .hero-img {
    position: relative;
    z-index: 2;  /* Pets stay above green box */
    max-width: 100%;
    height: 500px;

    }
        /* Paw print decorations */
        .paw-left {
        position: absolute;
        bottom: 20px;
        left: 20px;
        opacity: 0.2;
        width: 80px;
        }
        .paw-right {
        position: absolute;
        top: 20px;
        right: 20px;
        opacity: 0.2;
        width: 80px;
        }
    .greenbox {
    background-color: #8f9e73;
    width: 100%;       /* make it narrower than full width */
    height: 121px;    /* box height */
    position: absolute;
    bottom: 20px;        /* stick to bottom behind pets */
    left: 50%;
    transform: translateX(-50%);
    
    z-index: 1;       /* behind image */
    }
    .services {
        background: linear-gradient(to bottom, #f6edca 0%, #ffffff 100%);
        position: relative;
        width: 100%;
    }
    .services h1 {
        font-family: 'Pacifico', cursive;
        font-size: 56px;
    }
    .wside {
        width: 100%;
        display: grid;
        
        gap: 8px;
    }

    .sidedog {
        grid-area: sidebar;
        margin-top: 400px !important;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 400px; /* adjust as needed */
    }
    .sidedog::before {
            content: "";
    display: block;
    position: absolute;
   left: -358px;
    top: 179px;
    /* right: 38px; */
    bottom: 0;
    background-image: url(assets/doggyhd.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 600px;
    height: 300px;
    z-index: 1;
    }

    .mainform{
        grid-area: body;
    }
    .pet-form-container {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
    padding: 36px 32px 32px 32px;
    max-width: 900px;
    
    }

    .pet-form {
    width: 100%;
    }

    .toggle-group {
    display: flex;
    justify-content: center;
    gap: 40px;
    background: #f3eac2;
    border-radius: 10px;
    padding: 8px 0;
    margin-bottom: 28px;
    font-size: 22px;
    font-weight: 500;
    }
    .toggle-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #444;
    }
    .toggle-group input[type="radio"] {
    accent-color: #8a9a5b;
    width: 20px;
    height: 20px;
    }

    .service-buttons {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-bottom: 32px;
    flex-wrap: wrap;
    }
    .service-buttons button {
    flex: 1;
    min-width: 140px;
    background: #f3eac2;
    border: 1px solid #d6d1b1;
    border-radius: 8px;
    padding: 12px 8px;
    font-size: 18px;
    color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: background 0.2s;
    }
    .service-buttons button:hover {
    background: #ede5c0;
    }
    .icon {
    font-size: 22px;
    }

    .form-row {
    display: flex;
    gap: 24px;
    margin-bottom: 18px;
    flex-wrap: wrap;
    }
    .form-group {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    }
    .form-group label {
    font-size: 16px;
    color: #222;
    margin-bottom: 6px;
    font-weight: 500;
    }
    .form-group input {
    padding: 10px 12px;
    border: 1.5px solid #d6d1b1;
    border-radius: 7px;
    font-size: 16px;
    outline: none;
    background: #fff;
    transition: border 0.2s;
    }
    .form-group input:focus {
    border-color: #8a9a5b;
    }

    .photo-gallery-section {
    margin: 28px 0 18px 0;
    }
    .photo-gallery-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 16px;
    font-weight: 500;
    color: #8a9a5b;
    }
    .gallery-icon {
    font-size: 20px;
    }
    .gallery-desc {
    font-size: 13px;
    color: #888;
    margin-bottom: 10px;
    margin-left: 2px;
    }
    .gallery-images {
    display: flex;
    gap: 18px;
    margin-bottom: 6px;
    flex-wrap: wrap;
    }
    .gallery-img {
    width: 120px;
    height: 100px;
    background: #f5f5f5;
    border: 2px dashed #d6d1b1;
    border-radius: 8px;
    }
    .gallery-note {
    font-size: 11px;
    color: #888;
    margin-left: 2px;
    }
    /* CSS */
.paw-banner {
  position: relative;
  width: 100%;
  min-height: 160px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.paw-title {
  font-family: 'Pacifico', cursive;
  font-size: 48px;
  color: #222;
  margin: 60px 0 60px 0;
  z-index: 2;
  text-align: center;
  width: 100%;
}

.paw-left2 {
  position: absolute;
  left: 0;
  top: 20px;
  width: 90px;
  opacity: 0.7;
  z-index: 1;
}

.paw-right2 {
  position: absolute;
  right: 60px;
  top: 55px;
  width: 60px;
  opacity: 0.7;
  z-index: 1;
}
/* CSS */
.peace-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 40px 0 0 0;
  min-height: 420px;
  position: relative;
  font-family: 'Inter', Arial, sans-serif;
}

.peace-content {
  flex: 1 1 0;
  max-width: 600px;
  padding-left: 60px;
  position: relative;
}

.peace-title {
  color: #6b8a4b;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 24px;
  font-family: inherit;
}

.peace-list {
  color: #555;
  font-size: 18px;
  margin-bottom: 24px;
  padding-left: 24px;
}

.peace-list li {
  margin-bottom: 14px;
  line-height: 1.6;
}

.peace-list a {
  color: #6b8a4b;
  text-decoration: underline;
  font-weight: 500;
}

.peace-note {
  font-size: 13px;
  color: #888;
  margin-top: 18px;
  line-height: 1.4;
}

.peace-image {
  flex: 1 1 0;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  min-width: 340px;
  padding-right: 40px;
}

.peace-image img {
  max-width: 420px;
  width: 100%;
  height: auto;
  display: block;
  border-radius: 50% 50% 50% 50% / 55% 55% 45% 45%;
  
}

/* Decorative lines on left */
.peace-content::before {
  content: "";
  display: block;
  position: absolute;
  left: -40px;
  top: 18px;
  width: 32px;
  height: 90px;
  background: repeating-linear-gradient(
    to bottom,
    #bfc7b2,
    #bfc7b2 8px,
    transparent 8px,
    transparent 20px
  );
  border-radius: 4px;
  opacity: 0.7;
}

/* Responsive */
@media (max-width: 900px) {
  .peace-section {
    flex-direction: column;
    align-items: flex-start;
    padding: 30px 0 0 0;
  }
  .peace-content {
    max-width: 100%;
    padding-left: 30px;
    padding-right: 30px;
  }
  .peace-image {
    justify-content: center;
    padding-right: 0;
    margin-top: 30px;
    width: 100%;
  }
  .peace-image img {
    max-width: 320px;
  }
  .peace-content::before {
    left: -20px;
  }
}

/* Responsive for mobile */
@media (max-width: 600px) {
  .paw-title {
    font-size: 28px;
  }
  .paw-left2, .paw-right2 {
    width: 50px;
  }
  .paw-right2 {
    right: 10px;
    top: 30px;
  }
}

    .dog-size-section {
    margin: 18px 0 32px 0;
    }
    .dog-size-section label {
    font-size: 16px;
    color: #222;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    }
    .dog-size-options {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    flex-wrap: wrap;
    }
    .dog-size-options button {
    flex: 1;
    min-width: 120px;
    background: #fff;
    border: 1.5px solid #d6d1b1;
    border-radius: 8px;
    padding: 12px 8px;
    font-size: 16px;
    color: #222;
    cursor: pointer;
    transition: background 0.2s, border 0.2s;
    }
    .dog-size-options button:hover {
    background: #f3eac2;
    border-color: #8a9a5b;
    }

    .submit-btn {
    width: 100%;
    background: #8a9a5b;
    color: #fff;
    font-size: 28px;
    border: none;
    border-radius: 12px;
    padding: 12px 0;
    margin-top: 12px;
    cursor: pointer;
    font-family: inherit;
    transition: background 0.2s;
    }
    .submit-btn:hover {
    background: #7a8a4b;
    }

    .services .rightpaw {
    position: absolute;
        bottom: 792px;
        right: 3px;
        opacity: 0.8;
        width: 115px;
        height: 213px;
    }

    /* Tablet Styles */
    @media (max-width: 992px) {
    .wside {
        grid:
        "body" auto
        "sidebar" auto
        / 1fr;
        gap: 40px;
    }
    
    .sidedog {
        margin-top: 0 !important;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtext {
        font-size: 1.2rem;
    }
    
    .services h1 {
        font-size: 48px;
    }
    
    .services .rightpaw {
        bottom: -200px;
        right: 10px;
        width: 100px;
        height: 185px;
    }
    }

    /* Mobile Styles */
    @media (max-width: 768px) {
    .navbar-brand img {
        height: 40px;
    }
    
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-subtext {
        font-size: 1.1rem;
    }
    
    .hero-img {
        height: 300px;
    }
    
    .greenbox {
        height: 80px;
    }
    
    .paw-left, .paw-right {
        width: 60px;
    }
    
    .services h1 {
        font-size: 36px;
        padding: 0 20px;
    }
    
    .pet-form-container {
        margin: 0 15px;
        padding: 24px 20px;
    }
    
    .toggle-group {
        gap: 20px;
        font-size: 18px;
    }
    
    .service-buttons {
        flex-direction: column;
        gap: 12px;
    }
    
    .service-buttons button {
        min-width: 100%;
        font-size: 16px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    .gallery-images {
        justify-content: center;
        gap: 12px;
    }
    
    .gallery-img {
        width: 100px;
        height: 80px;
    }
    
    .dog-size-options {
        flex-direction: column;
        gap: 12px;
    }
    
    .dog-size-options button {
        min-width: 100%;
    }
    
    .submit-btn {
        font-size: 24px;
    }
    
    .services .rightpaw {
        display: none;
    }
    }

    /* Small Mobile Styles */
    @media (max-width: 480px) {
    .hero-title {
        font-size: 1.6rem;
    }
    
    .hero-subtext {
        font-size: 1rem;
    }
    
    .services h1 {
        font-size: 28px;
    }
    
    .pet-form-container {
        margin: 0 10px;
        padding: 20px 15px;
    }
    
    .toggle-group {
        gap: 15px;
        font-size: 16px;
        flex-direction: column;
        align-items: center;
    }
    
    .gallery-images {
        flex-direction: column;
        align-items: center;
    }
    
    .gallery-img {
        width: 120px;
        height: 100px;
    }
    
    .submit-btn {
        font-size: 20px;
    }
    }

    /* Navigation responsiveness */
    @media (max-width: 992px) {
    .navbar-nav {
        text-align: center;
        margin: 20px 0;
    }
    
    .right-links {
        display: flex !important;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
    }
    
    .right-links a {
        margin-left: 0;
    }
    }

    .services-showcase {
        background-color: #fff;
        padding: 80px 0;
        position: relative;
        }

        .showcase-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        position: relative;
        }

        .center-image {
position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    max-width: 500px;
    width: 100%;
    height: 850px;
        }

        .services-grid {
        display: grid;
        grid-template-columns: 1fr 400px 1fr;
        grid-template-rows: repeat(3, 1fr);
        gap: 40px;
        min-height: 600px;
        align-items: center;
        }

        .service-card {
        background: transparent;
        padding: 20px;
        text-align: center;
        position: relative;
        }

        .service-card h3 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 15px;
        }

        .service-card p {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin: 0;
        }

        .service-icon {
        width: 80px;
        height: 80px;
        background-color: #d4c49a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 32px;
        }

        /* Positioning for left side services */
        .doggy-walking {
        grid-column: 1;
        grid-row: 1;
        justify-self: end;
        text-align: right;
        }

        .doggy-walking .service-icon {
        margin: 0 0 20px auto;
        }

        .doggy-day-care {
        grid-column: 1;
        grid-row: 2;
        justify-self: end;
        text-align: right;
        }

        .doggy-day-care .service-icon {
        margin: 0 0 20px auto;
        }

        /* Positioning for right side services */
        .boarding {
        grid-column: 3;
        grid-row: 1;
        justify-self: start;
        text-align: left;
        }

        .boarding .service-icon {
        margin: 0 auto 20px 0;
        }

        .house-sitting {
        grid-column: 3;
        grid-row: 2;
        justify-self: start;
        text-align: left;
        }

        .house-sitting .service-icon {
        margin: 0 auto 20px 0;
        }

        /* Bottom center service */
        .drop-in-visits {
        grid-column: 2;
        grid-row: 3;
        justify-self: center;
        text-align: center;
        margin-top: 100px;
        }

        /* Icons - You can replace these with your local icons */
        .icon-paw::before {  content: url('assets/Group\ 170.png');  }
        .icon-sun::before {  content: url('assets/sunrise\ 4.png    ');  }
        .icon-briefcase::before {  content: url('assets/briefcase\ \(1\).png');  }
        .icon-home::before {  content: url('assets/home.png');  }
        .icon-location::before {  content: url('assets/Group\ 169.png');  }

        /* Responsive Design */
        @media (max-width: 992px) {
        .services-grid {
            grid-template-columns: 1fr;
            grid-template-rows: auto;
            gap: 50px;
            min-height: auto;
        }

        .center-image {
            position: static;
            transform: none;
            max-width: 300px;
            margin: 0 auto 40px;
            display: block;
        }

        .service-card {
            text-align: center;
            justify-self: center !important;
            max-width: 400px;
            margin: 0 auto;
        }

        .service-card .service-icon {
            margin: 0 auto 20px !important;
        }

        .doggy-walking,
        .doggy-day-care,
        .boarding,
        .house-sitting {
            text-align: center;
        }

        .drop-in-visits {
            margin-top: 0;
        }
        }

        @media (max-width: 768px) {
        .services-showcase {
            padding: 60px 0;
        }

        .showcase-container {
            padding: 0 15px;
        }

        .center-image {
            max-width: 250px;
        }

        .service-card h3 {
            font-size: 20px;
        }

        .service-card p {
            font-size: 13px;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }

        .services-grid {
            gap: 40px;
        }
        }

        @media (max-width: 480px) {
        .center-image {
            max-width: 200px;
        }

        .service-card h3 {
            font-size: 18px;
        }

        .service-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        }


        .green-btn {
  width: 420px;
  max-width: 90vw;
  background: #8f9e73;
  color: #fff;
  font-size: 18px;
  font-family: inherit;
  font-weight: 400;
  border: none;
  border-radius: 7px;
  padding: 12px 0;
  cursor: pointer;
  transition: background 0.18s;
  letter-spacing: 0.01em;
}

.green-btn:hover,
.green-btn:focus {
  background: #7e8e5e;
  outline: none;
}

/* Responsive */
@media (max-width: 500px) {
  .green-btn {
    width: 98vw;
    font-size: 16px;
    padding: 10px 0;
  }
}


.cont-green-btn  {
    display: flex;
  flex-direction: column;
  align-items: center;
  gap: 14px;
  margin: 30px 0;
}




/* CSS */
.reviews-section {
  position: relative;
  width: 83%;

  margin: 90px;
  padding: 0;
  background: transparent;
  overflow-x: hidden;
}

.reviews-dog-img {
  position: absolute;
  left: 40px;
  top: -30px;
  width: 170px;
  z-index: 3;
  pointer-events: none;
}

.reviews-title {
  font-family: 'Pacifico', cursive;
  font-size: 48px;
  text-align: center;
  margin: 60px 0 18px 0;
  color: #222;
  z-index: 2;
  position: relative;
}

.reviews-bg {
  
  min-width: 800px;
  background: #ede5c0;
  border-radius: 140px;
  margin: 0 auto;
  padding: 60px 0 40px 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 0 0 0 transparent;
  display: flex;
  justify-content: center;
}

.reviews-cards {
  display: flex;
  gap: 32px;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  max-width: 1100px;
  z-index: 2;
}

.review-card {
  background: #fffbe9;
  border-radius: 12px;
  box-shadow: 0 8px 24px 0 rgba(0,0,0,0.07);
  width: 230px;
  min-height: 270px;
  padding: 24px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: none;
  transition: box-shadow 0.2s, border 0.2s;
  box-sizing: border-box;
}

.review-card-active {
  border: 3px solid #3e7ad3;
  box-shadow: 0 8px 32px 0 rgba(62,122,211,0.13);
  background: #fff;
}

.review-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #fff;
  margin-top: -54px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 4px solid #fff;
  z-index: 2;
  position: relative;
}

.review-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.review-name {
  font-weight: 600;
  font-size: 16px;
  color: #222;
  margin-bottom: 8px;
  text-align: center;
}

.review-text {
  font-size: 15px;
  color: #444;
  text-align: center;
  margin-bottom: 18px;
  line-height: 1.5;
  min-height: 60px;
}

.review-stars {
  margin-top: auto;
  text-align: center;   
}

.review-stars img {
  width: 110px;
  height: 22px;
  object-fit: contain;
  display: inline-block;
}

/* Responsive */
@media (max-width: 1100px) {
  .reviews-cards {
    gap: 18px;
    max-width: 98vw;
  }
  .review-card {
    width: 180px;
    padding: 18px 8px 14px 8px;
  }
  .reviews-bg {
    min-width: 0;
    border-radius: 60px;
    padding: 40px 0 30px 0;
  }
  .reviews-section {
    min-width: 0;
  }
  .reviews-dog-img {
    width: 110px;
    left: 10px;
    top: -20px;
  }
  .reviews-title {
    font-size: 32px;
    margin-top: 40px;
  }
}

@media (max-width: 800px) {
  .reviews-cards {
    flex-wrap: wrap;
    gap: 16px;
  }
  .review-card {
    width: 98vw;
    max-width: 320px;
    margin: 0 auto;
  }
  .reviews-bg {
    border-radius: 30px;
    padding: 30px 0 20px 0;
  }
}
/* CSS */
.steps-section {
  width: 100%;
  
  background: radial-gradient(ellipse at 90% 60%, #f6edca 0%, #fff 60%);
  padding: 0 0 40px 0;
  margin: 0;
  position: relative;
  overflow-x: hidden;
}
*, *::before, *::after {
  box-sizing: border-box;
}

.steps-title {
  font-family: 'Pacifico', cursive;
  font-size: 40px;
  text-align: center;
  color: #222;
  margin: 40px 0 40px 0;
  font-weight: 400;
}

.steps-cards {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 48px;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  z-index: 2;
}

.step-card {
  background: #fff;
  border-radius: 50%;
  border: 2.5px solid #222;
  width: 320px;
  height: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.07);
  padding-top: 60px;
  box-sizing: border-box;
  text-align: center;
  transition: box-shadow 0.2s;
}

.step-avatar {
  position: absolute;
  top: -38px;
  left: 50%;
  transform: translateX(-50%);
  width: 76px;
  height: 76px;
  border-radius: 50%;
  border: 5px solid #fff;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 2;
}

.step-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.step-number {
  font-size: 22px;
  font-weight: 700;
  color: #222;
  margin-top: 18px;
  margin-bottom: 10px;
}

.step-desc {
  font-size: 15px;
  color: #444;
  line-height: 1.5;
  padding: 0 24px;
  margin-top: 0;
}

@media (max-width: 1100px) {
  .steps-cards {
    gap: 18px;
    max-width: 98vw;
  }
  .step-card {
    width: 220px;
    height: 220px;
    padding-top: 44px;
  }
  .step-avatar {
    width: 54px;
    height: 54px;
    top: -28px;
  }
  .steps-title {
    font-size: 28px;
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

@media (max-width: 800px) {
  .steps-cards {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
  .step-card {
    width: 98vw;
    max-width: 320px;
    height: 220px;
    border-radius: 50%;
    margin: 0 auto;
  }
}


/* mobile one  */
.connected-section {
  width: 100%;
  background: #fff;
  position: relative;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.connected-bg {
  width: 100%;
  background: #747b68;
  min-height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.connected-inner {
  width: 100%;
  max-width: 1200px;
  min-height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.connected-center {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  z-index: 2;
  width: 100%;
  max-width: 700px;
  gap: 60px;
}

/* Mobile container with white background */
.mobile-container {
  
  border-radius: 24px;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
 
}

.connected-mobile {
  width: 180px;
  max-width: 25vw;
  z-index: 2;
  /* Rotate the mobile phone */
  transform: rotate(-8deg);
  transition: transform 0.3s ease;
}

.connected-mobile:hover {
  transform: rotate(-5deg) scale(1.02);
}

.connected-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  min-width: 280px;
}

.connected-title {
  font-family: 'Pacifico', cursive;
  color: #fff6d6;
  font-size: 42px;
  font-weight: 400;
  margin-bottom: 20px;
  text-align: left;
  line-height: 1.1;
  letter-spacing: 0.01em;
}

.connected-stores {
  display: flex;
  gap: 32px;
  margin-top: 12px;
}

.connected-stores div {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 16px;
  font-family: 'Inter', Arial, sans-serif;
  opacity: 0.95;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.connected-stores div:hover {
  opacity: 1;
}

.connected-stores i {
  font-size: 20px;
  color: #fff;
}

.connected-dog {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: auto;
  max-width: 340px;
  z-index: 3;
  pointer-events: none;
  object-fit: cover;
}

/* Responsive */
@media (max-width: 1100px) {
  .connected-inner {
    min-height: 200px;
  }
  
  .mobile-container {
    padding: 30px;
  }
  
  .connected-mobile {
    width: 140px;
  }
  
  .connected-dog {
    max-width: 200px;
  }
  
  .connected-title {
    font-size: 28px;
  }
  
  .connected-center {
    gap: 30px;
    max-width: 500px;
  }
}

@media (max-width: 700px) {
  .connected-inner {
    flex-direction: column;
    align-items: center;
    min-height: 280px;
    padding: 40px 20px;
  }
  
  .connected-center {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    max-width: 95vw;
  }
  
  .mobile-container {
    padding: 25px;
    margin-bottom: 20px;
  }
  
  .connected-mobile {
    margin: 0;
    display: block;
    width: 120px;
    transform: rotate(-5deg);
  }
  
  .connected-content {
    align-items: center;
    text-align: center;
    margin: 0 auto;
    min-width: auto;
  }
  
  .connected-title {
    font-size: 24px;
    margin-bottom: 15px;
  }
  
  .connected-stores {
    gap: 25px;
    justify-content: center;
  }
  
  .connected-dog {
    position: static;
    width: 140px;
    margin: 20px auto 0;
    display: block;
    max-width: 140px;
    height: auto;
  }
}

.velvet-section {
  width: 100%;
 
  background: #f6edca;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  padding: 10px;
  margin: 0;
  font-family: 'Inter', Arial, sans-serif;
  overflow-x: hidden;
}

.velvet-left {
  width: 440px;
  min-width: 300px;
  background: #f6edca;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 38px 0 0 48px;
  position: relative;
  z-index: 2;
}

.velvet-logo {
  width: 90px;
  margin-bottom: 18px;
}

.velvet-title {
  font-size: 28px;
  color: #8a9a5b;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 1.1;
  letter-spacing: 0.01em;
}

.velvet-sub {
  font-weight: 400;
  color: #bfc7b2;
}

.velvet-countries {
  margin: 0 0 0 0;
  padding: 0 0 0 18px;
  color: #888;
  font-size: 18px;
  list-style: disc;
  line-height: 1.6;
}

.velvet-right {
  flex: 1 1 0;
  min-width: 0;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 38px 0 0 0;
}

.velvet-states-box {
  background: #fff;
  border-radius: 18px;
  padding: 32px 36px 32px 36px;
  margin-left: 40px;
  margin-top: 0;
  width: 558px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.04);
  min-width: 420px;
  max-width: 540px;
  z-index: 2;
}

.velvet-states-title {
  color: #8a9a5b;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 18px;
  line-height: 1.2;
}

.velvet-states-list {
  display: flex;
  gap: 38px;
  grid-template-columns: repeat(3, 1fr);
}

.velvet-states-list ul {
  margin: 0;
  padding: 0 0 0 18px;
  font-size: 17px;
  color: #444;
  list-style: disc;
  line-height: 1.5;
}

.velvet-paws {
  position: absolute;
  right: 220px;
  top: 18px;
  width: 70px;
  
  z-index: 3;
  pointer-events: none;
}

.velvet-dog {
    position: absolute;
    right: 186px;
    bottom: 292px;
    width: 320px;
    max-width: 32vw;
    z-index: 2;
    pointer-events: none;
}

/* Responsive */
@media (max-width: 1200px) {
  .velvet-dog {
    width: 220px;
    right: 0;
  }
  .velvet-paws {
    right: 140px;
    width: 50px;
  }
  .velvet-states-box {
    min-width: 0;
    max-width: 98vw;
    padding: 24px 6vw 24px 6vw;
  }
}

@media (max-width: 900px) {
  .velvet-section {
    flex-direction: column;
    min-width: 0;
  }
  .velvet-left {
    width: 100%;
    min-width: 0;
    padding: 28px 0 0 24px;
  }
  .velvet-right {
    padding: 0;
    margin: 0;
    min-height: 320px;
  }
  .velvet-dog {
    width: 160px;
    right: 0;
    bottom: 0;
  }
  .velvet-paws {
    right: 80px;
    top: 10px;
    width: 38px;
  }
}  

.main-footer {
  background: #748b68;
  padding: 0;
  margin: 0;
  width: 100%;
  position: relative;
  font-family: 'Inter', Arial, sans-serif;
  overflow: hidden;
}

.footer-inner {
  display: flex;
  align-items: flex-start;
  position: relative;
  min-height: 180px;
  padding: 0 0 0 0;
}

.footer-dog {
  position: absolute;
  left: 40px;
  top: -48px;
  width: 160px;
  z-index: 2;
  pointer-events: none;
}

.footer-links {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  margin-left: 220px;
  padding: 38px 0 24px 0;
  gap: 80px;
}

.footer-learn {
  min-width: 180px;
  color: #fff6d6;
  font-size: 18px;
}

.footer-title {
  font-weight: 700;
  font-size: 20px;
  color: #fff6d6;
  margin-bottom: 8px;
}

.footer-learn ul {
  margin: 0 0 0 18px;
  padding: 0;
  list-style: disc;
  color: #fff;
  font-size: 16px;
  line-height: 1.7;
}

.footer-tips {
  color: #fff6d6;
  font-size: 18px;
  max-width: 420px;
}

.footer-tips .footer-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #fff6d6;
}

.footer-desc {
  color: #fff;
  font-size: 15px;
  margin-top: 8px;
  line-height: 1.5;
}

.footer-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 15px;
  background: none;
  padding: 12px 48px 8px 220px;
  border-top: 1px solid rgba(255,255,255,0.08);
}

@media (max-width: 900px) {
  .footer-inner {
    flex-direction: column;
    min-height: 0;
    padding-left: 0;
  }
  .footer-dog {
    position: static;
    width: 120px;
    margin: -40px 0 0 0;
    display: block;
  }
  .footer-links {
    flex-direction: column;
    margin-left: 0;
    padding: 38px 20px 24px 20px;
    gap: 30px;
  }
  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 20px 8px 20px;
    font-size: 14px;
    gap: 6px;
  }
}




</style>
    </head>
    <body>

    <!-- Header -->
    <nav class="navbar navbar-expand-lg">
    <div class="container">
        <!-- Logo -->
    
        <!-- Toggle for mobile -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent">
        <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Center links -->
        <div class="collapse navbar-collapse justify-content-center" id="navbarContent">
            <a class="navbar-brand" href="#">
        <img src="./assets/logo.png" alt="Logo">
        </a>

            <ul class="navbar-nav mx-5">
        
            <li class="nav-item ">
            <a class="nav-link" href="#"><i class="bi bi-search"></i> Search sitter</a>
            </li>
            <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-heart"></i> Become sitter</a>
            </li>
            <li class="nav-item">
            <a class="nav-link" href="#"><img src="./assets/Group.png" alt="" srcset=""> Our services</a>
            </li>
        </ul>
        </div>

        <!-- Right links -->
        <div class="right-links d-none d-lg-flex">
        <a href="#" class="nav-link">Sign in</a>
        <a href="#" class="nav-link">Sign up</a>
        <a href="#" class="nav-link"><i class="bi bi-question-circle"></i> Help</a>
        </div>
    </div>
    </nav>

    <section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
        <!-- Left content -->
        <div class="col-lg-6 text-center text-lg-start">
            <h1 class="hero-title">Local Trusted,<br>and Tail-Wagging</h1>
            <p class="hero-subtext">Book trusted sitters and dog walkers</p>
            <a href="#" class="btn btn-custom">Become a local sitter</a>
        </div>

        <!-- Right image + green box -->
        <div class="col-lg-6 text-center position-relative">
            <img src="./assets/all ikamgbe 1.png" alt="Pets" class="hero-img">
        </div>
        <div class="greenbox"></div>
        </div>
    </div>

    <!-- Decorative paw prints -->
    <img src="assets/Group 193.png" alt="Paw print" class="paw-left">
    <img src="assets/Group 214.png" alt="Paw print" class="paw-right">
    </section>

    <section class="services justify-content-center align-items-center">    <div class="container">
        <div class="text-center">
            <h1>Services for every dog & cat</h1>
        </div><br><br>
        
        <section class="wside justify-content-center align-items-center">
            <div class="mainform justify-content-center align-items-center">
            <div class="pet-form-container justify-content-center align-items-center">
                <form class="pet-form">
                
                <div class="toggle-group">
                    <label>
                    <input type="radio" name="pet-type" checked>
                    <span>Dog</span>
                    </label>
                    <label>
                    <input type="radio" name="pet-type">
                    <span>Cat</span>
                    </label>
                </div>

                <div class="service-buttons">
                    <button type="button"><span class="icon">📦</span> Boarding</button>
                    <button type="button"><span class="icon">🏠</span> House Sitting</button>
                    <button type="button"><span class="icon">👤</span> Drop In Visits</button>
                    <button type="button"><span class="icon">🌞</span> Doggy Day Care</button>
                    <button type="button"><span class="icon">🐾</span> Doggy walking</button>
                </div>

                <!-- Form Fields -->
                <div class="form-row">
                    <div class="form-group">
                    <label>Your Name</label>
                    <input type="text" placeholder="Your Name">
                    </div>
                    <div class="form-group">
                    <label>Contact Info</label>
                    <input type="text" placeholder="Contact Info">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                    <label>Address</label>
                    <input type="text" placeholder="Address">
                    </div>
                    <div class="form-group">
                    <label>Drop Off</label>
                    <input type="text" placeholder="Drop Off">
                    </div>
                    <div class="form-group">
                    <label>Pick Up</label>
                    <input type="text" placeholder="Pick Up">
                    </div>
                </div>

                <!-- Photo Gallery -->
                <div class="photo-gallery-section">
                    <div class="photo-gallery-label">
                    <span class="gallery-icon">📷</span>
                    <span>Photo gallery</span>
                    </div>
                    <div class="gallery-desc">Show off your pet through photos</div>
                    <div class="gallery-images">
                    <div class="gallery-img"></div>
                    <div class="gallery-img"></div>
                    <div class="gallery-img"></div>
                    </div>
                    <div class="gallery-note">Upload up to 3 photos of your pets.</div>
                </div>

                <!-- Dog Size -->
                <div class="dog-size-section">
                    <label>My dog size</label>
                    <div class="dog-size-options">
                    <button type="button">Small 0-15 lbs</button>
                    <button type="button">Medium 16-40 lbs</button>
                    <button type="button">Large 41-100 lbs</button>
                    <button type="button">Giant 101+ lbs</button>
                    </div>
                </div>

                <!-- Submit -->
                <button type="submit" class="submit-btn">Submit</button>
                
                </form>
            </div>
            </div> 

        <div class="sidedog"></div>
        </section>
        
        
        <img src="./assets/Group 213.png" alt="" class="rightpaw">
    </div>
    </section>
    <section class="services-showcase">
    <div class="showcase-container">
        
        <!-- Center Woman with Dog Image -->
        <img src="./assets/woman-discussing-and-training-her-dalmatian-pet-in-LE86V5Ha-1 1 (2).png" alt="Woman with dog" class="center-image">
        
        <div class="services-grid">
        
        <!-- Doggy Walking - Top Left -->
        <div class="service-card doggy-walking">
            <div class="service-icon icon-paw"></div>
            <h3>Doggy Walking</h3>
            <p>Your sitter drops by your home to play with your pets, offer food, and give potty breaks or clean the litter box.</p>
        </div>

        <!-- Boarding - Top Right -->
        <div class="service-card boarding">
            <div class="service-icon icon-briefcase"></div>
            <h3>Boarding</h3>
            <p>Your pets stay overnight in your sitter's home. They'll be treated like part of the family in a comfortable environment.</p>
        </div>

        <!-- Doggy Day Care - Bottom Left -->
        <div class="service-card doggy-day-care">
            <div class="service-icon icon-sun"></div>
            <h3>Doggy Day Care</h3>
            <p>Your dog gets a walk around your neighbourhood. Perfect for busy days and dogs with extra energy to burn.</p>
        </div>

        <!-- House Sitting - Bottom Right -->
        <div class="service-card house-sitting">
            <div class="service-icon icon-home"></div>
            <h3>House Sitting</h3>
            <p>Your sitter takes care of your pets and your house. Your pets will get all the attention they need from the comfort of home.</p>
        </div>

        <!-- Drop in Visits - Bottom Center -->
        <div class="service-card drop-in-visits">
            <div class="service-icon icon-location"></div>
            <h3>Drop in visits</h3>
            <p>Your dog gets a walk around your neighbourhood. Perfect for busy days and dogs with extra energy to burn.</p>
        </div>

        </div>
    </div>
    </section>


    <section>
<section class="paw-banner">
  <img src="assets/Group 196.png" alt="Left Paw" class="paw-left2">
  <h2 class="paw-title">Velvet Leash Co. Protect</h2>
  <img src="assets/Group 195.png" alt="Right Paw" class="paw-right2">
</section>
<!-- HTML -->
<section class="peace-section justify-content-center">
  <div class="peace-content">
    <h2 class="peace-title">Find peace of mind with every booking.</h2>
    <ul class="peace-list">
      <li>Screened pet sitters have already passed a third-party background check and show verified reviews from other pet parents, like you.</li>
      <li>Messaging &amp; photo updates from your sitter during each stay.</li>
      <li>The Velvet Leash Co. Guarantee can protect you and your pet for up to $25,000 in eligible vet care. <a href="#">Learn more</a></li>
      <li>24/7 support from the Velvet Leash Co. team—here to help if you ever need someone to talk to.</li>
    </ul>
    <div class="peace-note">
      • Services booked on Velvet Leash Co. are backed by Velvet Leash Co. Protect and reservation protection. Modified terms apply to bookings with day care carets. Good Pup not included
    </div>
  </div>
  <div class="peace-image">
    <img src="assets/women-pet.PNG" alt="Woman with dog and puppies">
  </div>
</section>
<div class="cont-green-btn">

  <button class="green-btn">Book a local sitter</button>
  <button class="green-btn">Learn more about Velvet Leash Co. Protect</button>
</div>
    </section>




    <!-- HTML -->
<section class="reviews-section">
  <img src="assets/top-doggy.png" alt="Dog" class="reviews-dog-img">
  <h2 class="reviews-title">Reviews</h2>
  <div class="reviews-bg">
    <div class="reviews-cards">
      <!-- Card 1 -->
      <div class="review-card review-card-active">
        <div class="review-avatar">
          <img src="assets/avatar1.png" alt="Avatar 1">
        </div>
        <div class="review-name">- Danielle H.</div>
        <div class="review-text">
          My sitter took great care of my cat, above and beyond my expectations. I would book with Velvet Leash Co. again in a heartbeat!
        </div>
        <div class="review-stars">
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
</div>
      </div>
      <!-- Card 2 -->
      <div class="review-card">
        <div class="review-avatar">
          <img src="assets/avatar2.png" alt="Avatar 2">
        </div>
        <div class="review-name">Doggy - Molly S.</div>
        <div class="review-text">
          I was nervous to leave Sam with strangers, but my worries quickly faded. Going forward Velvet Leash Co. will be my first choice for pet sitting.
        </div>
        <div class="review-stars">
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
</div>
      </div>
      <!-- Card 3 -->
      <div class="review-card">
        <div class="review-avatar">
          <img src="assets/avatar1.png" alt="Avatar 1">
        </div>
        <div class="review-name">- Danielle H.</div>
        <div class="review-text">
          My sitter took great care of my cat, above and beyond my expectations. I would book with Velvet Leash Co. again in a heartbeat!
        </div>
       <div class="review-stars">
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
</div>
      </div>
      <!-- Card 4 -->
      <div class="review-card">
        <div class="review-avatar">
          <img src="assets/avatar2.png" alt="Avatar 2">
        </div>
        <div class="review-name">Doggy - Molly S.</div>
        <div class="review-text">
          I was nervous to leave Sam with strangers, but my worries quickly faded. Going forward Velvet Leash Co. will be my first choice for pet sitting.
        </div>
        <div class="review-stars">
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
  <i class="fas fa-star" style="color:#000;"></i>
</div>
      </div>
    </div>
  </div>
</section>
<!-- HTML -->
<section class="steps-section">
  <h2 class="steps-title">Meet local sitters who will treat your <br> pets like family</h2>
  <div class="steps-cards">
    <!-- Card 1 -->
    <div class="step-card">
      <div class="step-avatar" style="background:#ffe7a7;">
        <img src="assets/person1.png" alt="Avatar 3">
      </div>
      <div class="step-number">1. Search</div>
      <div class="step-desc">
        Stay in touch with photos and messaging. Plus, your booking is backed by Velvet Leash Co. Protect, including 24/7 support and reimbursement for eligible vet care.
      </div>
    </div>
    <!-- Card 2 -->
    <div class="step-card">
      <div class="step-avatar" style="background:#fff;">
        <img src="assets/person2.png" alt="Avatar 2">
      </div>
      <div class="step-number">2. Book &amp; pay</div>
      <div class="step-desc">
        Stay in touch with photos and messaging. Plus, your booking is backed by Velvet Leash Co. Protect, including 24/7 support and reimbursement for eligible vet care.
      </div>
    </div>
    <!-- Card 3 -->
    <div class="step-card">
      <div class="step-avatar" style="background:#d6f0ff;">
        <img src="assets/person3.png" alt="Avatar 1">
      </div>
      <div class="step-number">3. Relax</div>
      <div class="step-desc">
        Stay in touch with photos and messaging. Plus, your booking is backed by Velvet Leash Co. Protect, including 24/7 support and reimbursement for eligible vet care.
      </div>
    </div>
  </div>
</section>


<section class="connected-section">
  <div class="connected-bg">
    <div class="connected-inner">
      <div class="connected-center">
        
        <div class="mobile-container">
          <img src="assets/mobile.png" alt="Mobile with logo" class="connected-mobile">
        </div>
        <div class="connected-content">
          <div class="connected-title">Always Connected<br>With Us</div>
          <div class="connected-stores">
            <div>
              <i class="fab fa-google-play"></i>
              <span>Play Store</span>
            </div>
            <div>
              <i class="fas fa-mobile-alt"></i>
              <span>App Store</span>
            </div>
          </div>
        </div>
      </div>
      <img src="assets/side-dog.png" alt="Dog on right" class="connected-dog">
    </div>
  </div>
</section>

<section class="velvet-section">
  <div class="velvet-left">
    <img src="assets/velvet-leash.png" alt="Velvet Leash Co Logo" class="velvet-logo">
    <div class="velvet-title">Velvet Leash Co<br><span class="velvet-title">. is also in</span></div>
    <ul class="velvet-countries">
      <li>Canada</li>
      <li>United Kingdom</li>
      <li>France</li>
      <li>Germany</li>
      <li>Italy</li>
      <li>Spain</li>
      <li>Netherlands</li>
      <li>Norway</li>
      <li>Sweden</li>
      <li>Ireland</li>
      <li>Australia</li>
      <li>Brazil</li>
    </ul>
  </div>
  <div class="velvet-right">
    <div class="velvet-states-box">
      <div class="velvet-states-title">Thousands of pet<br>sitters across the<br>United States.</div>
      <div class="velvet-states-list">
        <ul>
          <li>Alabama</li>
          <li>Alaska</li>
          <li>Arizona</li>
          <li>Arkansas</li>
          <li>California</li>
          <li>Colorado</li>
          <li>Connecticut</li>
          <li>Delaware</li>
          <li>District of Columbia</li>
          <li>Florida</li>
          <li>Georgia</li>
          <li>Hawaii</li>
          <li>Idaho</li>
          <li>Illinois</li>
          <li>Indiana</li>
          <li>Iowa</li>
          <li>Kansas</li>
          <li>Kentucky</li>
          <li>Louisiana</li>
          <li>Maine</li>
          <li>Maryland</li>
          <li>Massachusetts</li>
          <li>Michigan</li>
          <li>Minnesota</li>
        </ul>
        <ul>
          <li>Mississippi</li>
          <li>Missouri</li>
          <li>Montana</li>
          <li>Nebraska</li>
          <li>Nevada</li>
          <li>New Hampshire</li>
          <li>New Jersey</li>
          <li>New Mexico</li>
          <li>New York</li>
          <li>North Carolina</li>
          <li>North Dakota</li>
          <li>Ohio</li>
          <li>Oklahoma</li>
          <li>Oregon</li>
          <li>Pennsylvania</li>
          <li>Rhode Island</li>
          <li>South Carolina</li>
          <li>South Dakota</li>
          <li>Tennessee</li>
          <li>Texas</li>
          <li>Utah</li>
          <li>Virginia</li>
          <li>Washington</li>
          <li>West Virginia</li>
        </ul>
      </div>
    </div>
    <img src="assets/Group 196.png" alt="Paws" class="velvet-paws">
    <img src="assets/hd doggy (1).png" alt="Brown Dog" class="velvet-dog">
  </div>
</section>

<footer class="main-footer">
  <div class="footer-inner">
    <img src="assets/hd doggy (3).png" alt="Small Dog" class="footer-dog">
    <div class="footer-links">
      <div class="footer-learn">
        <div class="footer-title">Learn More</div>
        <ul>
          <li>Velvet Leash Co. Q&amp;A</li>
          <li>Community</li>
          <li>Safety</li>
        </ul>
        <div class="footer-title" style="margin-top:18px;">Need Help?</div>
        <ul>
          <li>Help Center</li>
        </ul>
      </div>
      <div class="footer-tips">
        <div class="footer-title">All the pet care tips you need—<br>right to your inbox</div>
        <div class="footer-desc">
          By providing my email address, I consent to receive marketing communications from VelvetLeashCo.com and its affiliates and confirm that I am 18 years of age or older. I can unsubscribe at any time. Privacy Statement
        </div>
      </div>
    </div>
  </div>
  <div class="footer-bottom">
    <div class="text-center">© 2025 A Place for Velvet Leash Co., Inc. All Rights Reserved.</div>
<div>Powered by Wolke Consultancy</div>
  </div>
</footer>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>